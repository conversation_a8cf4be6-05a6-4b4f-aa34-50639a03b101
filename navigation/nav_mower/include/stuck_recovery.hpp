#ifndef STUCK_RECOVERY_HPP
#define STUCK_RECOVERY_HPP

#include "data_type.hpp"
#include "utils/logger.hpp"

#include <atomic>
#include <chrono>
#include <deque>
#include <functional>
#include <memory>

namespace fescue_iox
{

// 脱困恢复模式枚举
enum class StuckRecoveryMode : int
{
    NONE = 0,           // 无脱困
    SINGLE_WHEEL_LEFT,  // 左轮单独旋转
    SINGLE_WHEEL_RIGHT, // 右轮单独旋转
    FORWARD,            // 前进
    BACKWARD,           // 后退
    ROTATE_LEFT,        // 左转
    ROTATE_RIGHT,       // 右转
    ALTERNATING_PUSH    // 交替推进
};

// 脱困状态枚举
enum class StuckRecoveryState : int
{
    NORMAL = 0, // 正常状态
    DETECTING,  // 检测中
    RECOVERING, // 恢复中
    COMPLETED   // 完成
};

// 运动数据结构
struct MotionData
{
    uint64_t timestamp;     // 时间戳(毫秒)
    float linear_velocity;  // 线速度
    float angular_velocity; // 角速度
    float displacement_x;   // X方向位移
    float displacement_y;   // Y方向位移
    float rotation;         // 旋转角度
};

// 脱困恢复参数
struct StuckRecoveryParam
{
    // 时间窗口参数
    uint32_t short_window_ms = 30000;   // 短时间窗口：30秒
    uint32_t medium_window_ms = 120000; // 中时间窗口：2分钟
    uint32_t long_window_ms = 300000;   // 长时间窗口：5分钟

    // 检测阈值
    float min_displacement_threshold = 0.1f; // 最小位移阈值(米)
    float min_rotation_threshold = 0.1f;     // 最小旋转阈值(弧度)

    // 打滑检测阈值
    float encoder_velocity_threshold = 0.05f;     // 编码器速度阈值(m/s)
    float imu_acceleration_threshold = 0.03f;     // IMU加速度阈值(m/s²)
    float imu_angular_velocity_threshold = 0.03f; // IMU角速度阈值(rad/s)

    // IMU积分参数
    float imu_filter_alpha = 0.8f;                 // 低通滤波系数
    float imu_acceleration_deadzone = 0.02f;       // 加速度死区(m/s²)
    float imu_velocity_decay_factor = 0.99f;       // 速度衰减系数
    float imu_integration_reset_threshold = 0.02f; // 积分重置阈值(m/s)

    // 速度参数
    float initial_linear_velocity = 0.1f;  // 初始线速度
    float initial_angular_velocity = 0.2f; // 初始角速度
    float max_linear_velocity = 0.4f;      // 最大线速度
    float max_angular_velocity = 1.0f;     // 最大角速度
    float velocity_increment = 0.05f;      // 速度递增步长

    // 恢复动作参数
    uint32_t action_duration_ms = 2000;  // 单次动作持续时间(毫秒)
    uint32_t action_interval_ms = 500;   // 动作间隔时间(毫秒)
    uint32_t max_recovery_attempts = 10; // 最大恢复尝试次数

    // 轮控参数
    float wheel_base = 0.335f; // 轮距(米)
    float wheel_radius = 0.1f; // 轮半径(米)
};

// 脱困恢复结果
struct StuckRecoveryResult
{
    bool is_stuck = false;             // 是否被困
    bool recovery_success = false;     // 恢复是否成功
    StuckRecoveryMode current_mode;    // 当前恢复模式
    uint32_t recovery_attempts = 0;    // 恢复尝试次数
    uint64_t recovery_duration_ms = 0; // 恢复持续时间
};

class StuckRecovery
{
public:
    explicit StuckRecovery(const StuckRecoveryParam &param = StuckRecoveryParam{});
    ~StuckRecovery();

    // 主要接口
    StuckRecoveryResult Update(const ImuData &imu_data, const MotorSpeedData &motor_speed_data);
    void Reset();
    void SetOriginalMode(ThreadControl mode) { original_mode_ = mode; }
    ThreadControl GetOriginalMode() const { return original_mode_; }

    // 参数设置
    void SetParam(const StuckRecoveryParam &param) { param_ = param; }
    StuckRecoveryParam GetParam() const { return param_; }

    // 速度发布回调
    void SetVelocityPublishCallback(std::function<void(float, float, uint64_t)> callback)
    {
        velocity_publish_callback_ = callback;
    }

    // 状态查询
    bool IsStuck() const { return state_ != StuckRecoveryState::NORMAL; }
    bool IsRecovering() const { return state_ == StuckRecoveryState::RECOVERING; }
    StuckRecoveryState GetState() const { return state_; }

private:
    // 核心检测和恢复逻辑
    bool DetectStuck();
    bool DetectWheelSlipping();
    void StartRecovery();
    void ExecuteRecoveryAction();
    void CheckRecoveryProgress();
    bool HasMovementInWindow(uint32_t window_ms);

    // 运动数据处理
    void UpdateMotionData(const ImuData &imu_data, const MotorSpeedData &motor_speed_data);
    void CalculateDisplacement(float linear_vel, float angular_vel, float dt);
    void CleanOldData(uint32_t max_age_ms);

    // IMU积分处理
    void InitializeImuIntegration(const ImuData &imu_data);
    float IntegrateImuVelocity(const ImuData &imu_data);
    void ResetImuIntegration();

    // 恢复动作执行
    void ExecuteSingleWheelRotation(bool left_wheel);
    void ExecuteForwardBackward(bool forward);
    void ExecuteRotation(bool left_rotation);
    void ExecuteAlternatingPush();
    void PublishVelocity(float linear, float angular, uint64_t duration_ms);

    // 模式切换
    StuckRecoveryMode GetNextRecoveryMode();
    void SwitchToNextMode();

private:
    StuckRecoveryParam param_;
    StuckRecoveryState state_;
    StuckRecoveryMode current_recovery_mode_;
    ThreadControl original_mode_;

    // 运动数据存储
    std::deque<MotionData> motion_history_;

    // 当前状态
    float current_linear_velocity_;
    float current_angular_velocity_;
    float accumulated_displacement_x_;
    float accumulated_displacement_y_;
    float accumulated_rotation_;

    // 编码器数据（用于打滑检测）
    float current_encoder_linear_velocity_;
    float current_encoder_angular_velocity_;
    ImuData current_imu_data_;
    MotorSpeedData current_motor_data_;

    // IMU积分相关
    float integrated_velocity_x_;
    float integrated_velocity_y_;
    float last_acceleration_x_;
    float last_acceleration_y_;
    uint64_t last_imu_timestamp_;
    bool imu_integration_initialized_;

    // 时间管理
    std::chrono::steady_clock::time_point last_update_time_;
    std::chrono::steady_clock::time_point recovery_start_time_;
    std::chrono::steady_clock::time_point last_action_time_;

    // 恢复状态
    uint32_t recovery_attempts_;
    bool recovery_in_progress_;

    // 回调函数
    std::function<void(float, float, uint64_t)> velocity_publish_callback_;

    // 互斥锁
    mutable std::mutex data_mutex_;
};

} // namespace fescue_iox

#endif // STUCK_RECOVERY_HPP
