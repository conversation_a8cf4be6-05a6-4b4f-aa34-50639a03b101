# 脱困恢复功能说明

## 概述

脱困恢复功能是一个独立的类，专门用于检测割草机在割草作业过程中的被困状态，并执行相应的恢复措施。该功能适用于单目割草机，使用IMU和编码器数据进行运动检测。

## 功能特性

### 1. 智能分段检测
- **短时间窗口（30秒）**：检测瞬时卡滞
- **中时间窗口（2分钟）**：检测中等程度被困
- **长时间窗口（5分钟）**：检测长期被困状态
- **差异化判定**：至少两个窗口检测到问题才确认被困，避免误判
- **打滑检测**：结合编码器和IMU数据检测轮子打滑情况

### 2. 渐进式动力调整
- 从低线速度、低角速度开始
- 逐步提升到设定最大值
- 先尝试轻微动作，无效则加大力度

### 3. 多模式轮控切换
- **单轮点动**：一侧轮子高速旋转，另一侧慢速或静止
- **双轮同速前进/后退**：均速前进或后退，突破阻力区
- **交替推进**：前进一段、后退一段，多次重复"蠕动"脱出
- **原地旋转**：左转或右转尝试改变方向

### 4. 实时监控
- 实时检测是否产生实际移动
- 一旦检测到运动，立即中断脱困过程
- 自动恢复到原功能模式

## 使用方法

### 1. 基本使用

```cpp
#include "stuck_recovery.hpp"

// 创建脱困恢复实例
StuckRecoveryParam param;
param.short_window_ms = 30000;    // 30秒
param.medium_window_ms = 120000;  // 2分钟
param.long_window_ms = 300000;    // 5分钟

auto stuck_recovery = std::make_unique<StuckRecovery>(param);

// 设置速度发布回调
stuck_recovery->SetVelocityPublishCallback(
    [](float linear, float angular, uint64_t duration_ms) {
        // 发布速度命令
        PublishVelocity(linear, angular, duration_ms);
    });

// 在主循环中更新
ImuData imu_data = GetCurrentImuData();
MotorSpeedData motor_data = GetCurrentMotorData();

StuckRecoveryResult result = stuck_recovery->Update(imu_data, motor_data);

if (result.is_stuck) {
    // 处理被困状态
    LOG_INFO("检测到被困，恢复模式: {}", static_cast<int>(result.current_mode));
}

if (result.recovery_success) {
    // 脱困成功，恢复原功能
    LOG_INFO("脱困成功");
}
```

### 2. 参数配置

```cpp
StuckRecoveryParam param;

// 时间窗口设置
param.short_window_ms = 30000;   // 短窗口：30秒
param.medium_window_ms = 120000; // 中窗口：2分钟
param.long_window_ms = 300000;   // 长窗口：5分钟

// 检测阈值
param.min_displacement_threshold = 0.1f;  // 最小位移阈值(米)
param.min_rotation_threshold = 0.1f;      // 最小旋转阈值(弧度)

// 打滑检测阈值
param.encoder_velocity_threshold = 0.05f;     // 编码器速度阈值(m/s)
param.imu_acceleration_threshold = 0.03f;     // IMU加速度阈值(m/s²)
param.imu_angular_velocity_threshold = 0.03f; // IMU角速度阈值(rad/s)

// IMU积分参数
param.imu_filter_alpha = 0.8f;                 // 低通滤波系数(0-1)
param.imu_acceleration_deadzone = 0.02f;       // 加速度死区(m/s²)
param.imu_velocity_decay_factor = 0.99f;       // 速度衰减系数(0-1)
param.imu_integration_reset_threshold = 0.02f; // 积分重置阈值(m/s)

// 速度参数
param.initial_linear_velocity = 0.1f;     // 初始线速度
param.initial_angular_velocity = 0.2f;    // 初始角速度
param.max_linear_velocity = 0.4f;         // 最大线速度
param.max_angular_velocity = 1.0f;        // 最大角速度
param.velocity_increment = 0.05f;         // 速度递增步长

// 恢复动作参数
param.action_duration_ms = 2000;          // 单次动作持续时间(毫秒)
param.action_interval_ms = 500;           // 动作间隔时间(毫秒)
param.max_recovery_attempts = 10;         // 最大恢复尝试次数
```

### 3. 集成到NavigationMowerAlg

脱困恢复功能已经集成到`NavigationMowerAlg`类中：

```cpp
// 设置脱困恢复参数
StuckRecoveryParam param;
// ... 配置参数
mower_alg->SetStuckRecoveryParam(param);

// 检查脱困状态
if (mower_alg->IsStuckRecoveryActive()) {
    LOG_INFO("脱困恢复正在进行中");
}

// 重置脱困恢复
mower_alg->ResetStuckRecovery();
```

## 被困判定逻辑

系统使用智能的多条件判定逻辑，避免误判：

### 判定条件（满足任一即认为被困）：
1. **多窗口确认**：至少两个时间窗口检测到无运动
2. **打滑确认**：长时间窗口无运动 + 检测到轮子打滑
3. **全窗口确认**：所有三个窗口都检测到无运动

### 打滑检测原理：
- **编码器有运动**：轮子在转动（速度 > 0.05 m/s）
- **IMU无运动**：实际无位移（加速度 < 0.03 m/s²，角速度 < 0.03 rad/s）
- **判定打滑**：编码器显示运动但IMU显示静止

### IMU积分算法：
- **梯形积分法**：使用梯形积分计算速度，提高精度
- **低通滤波**：去除高频噪声，滤波系数可配置
- **死区处理**：忽略小幅度噪声，防止误积分
- **积分漂移抑制**：速度衰减机制，防止长期漂移
- **自动重置**：检测到异常时自动重置积分器

## 工作流程

1. **正常监控**：持续监控IMU和编码器数据，检测打滑
2. **智能检测**：使用多时间窗口和打滑检测进行综合判断
3. **开始恢复**：保存当前模式，开始执行恢复动作
4. **渐进恢复**：从低功率开始，逐步增加动力
5. **模式切换**：如果当前模式无效，切换到下一个恢复模式
6. **实时监控**：检测是否产生实际运动（基于IMU数据）
7. **恢复完成**：检测到运动后，恢复到原功能模式

## 恢复模式序列

1. **FORWARD** → 前进
2. **BACKWARD** → 后退
3. **ROTATE_LEFT** → 左转
4. **ROTATE_RIGHT** → 右转
5. **SINGLE_WHEEL_LEFT** → 左轮单独旋转
6. **SINGLE_WHEEL_RIGHT** → 右轮单独旋转
7. **ALTERNATING_PUSH** → 交替推进
8. 循环回到 **FORWARD**

## 适用场景

- **边切模式**：沿边界割草时遇到障碍物
- **探索模式**：探索未知区域时被困
- **随机割草**：随机运动时遇到复杂地形
- **螺旋割草**：螺旋运动时被困在角落

## 注意事项

1. **安全性**：脱困过程中会逐步增加动力，请确保周围环境安全
2. **传感器依赖**：需要IMU和编码器数据正常工作
3. **参数调优**：根据实际割草机特性调整检测阈值和速度参数
4. **异常处理**：如果长时间无法脱困，建议人工干预

## 测试

运行测试程序验证功能：

```bash
# 编译测试程序
cd navigation/nav_mower/test
g++ -std=c++17 test_stuck_recovery.cpp -o test_stuck_recovery -I../include

# 运行测试
./test_stuck_recovery
```

## 日志输出

脱困恢复过程会输出详细的日志信息：

```
[StuckRecovery] IMU积分初始化完成
[StuckRecovery] IMU积分: acc_x=0.120, acc_y=0.050, vel_x=0.080, vel_y=0.030, linear_vel=0.085
[StuckRecovery] 检测到轮子打滑: 编码器线速度=0.150, IMU加速度X=0.020, IMU角速度Z=0.010
[StuckRecovery] 多窗口检测到被困: 短窗口=true, 中窗口=true, 长窗口=false, 打滑=true
[StuckRecovery] 检测到被困状态，开始脱困恢复
[StuckRecovery] 执行恢复动作: FORWARD, 尝试次数: 1, 线速度: 0.10, 角速度: 0.20
[StuckRecovery] 切换恢复模式: FORWARD -> BACKWARD
[StuckRecovery] 检测到移动，脱困成功
[NavigationMowerAlg] 脱困成功，恢复到原模式: RANDOM_MOWING_THREAD
```
