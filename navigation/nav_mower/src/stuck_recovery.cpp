#include "stuck_recovery.hpp"

#include "utils/time.hpp"

#include <algorithm>
#include <cmath>

namespace fescue_iox
{

StuckRecovery::StuckRecovery(const StuckRecoveryParam &param)
    : param_(param)
    , state_(StuckRecoveryState::NORMAL)
    , current_recovery_mode_(StuckRecoveryMode::NONE)
    , original_mode_(ThreadControl::UNDEFINED)
    , current_linear_velocity_(0.0f)
    , current_angular_velocity_(0.0f)
    , accumulated_displacement_x_(0.0f)
    , accumulated_displacement_y_(0.0f)
    , accumulated_rotation_(0.0f)
    , current_encoder_linear_velocity_(0.0f)
    , current_encoder_angular_velocity_(0.0f)
    , integrated_velocity_x_(0.0f)
    , integrated_velocity_y_(0.0f)
    , last_acceleration_x_(0.0f)
    , last_acceleration_y_(0.0f)
    , last_imu_timestamp_(0)
    , imu_integration_initialized_(false)
    , recovery_attempts_(0)
    , recovery_in_progress_(false)
{
    last_update_time_ = std::chrono::steady_clock::now();
    LOG_INFO("[StuckRecovery] 脱困恢复系统初始化完成");
}

StuckRecovery::~StuckRecovery()
{
    LOG_INFO("[StuckRecovery] 脱困恢复系统销毁");
}

StuckRecoveryResult StuckRecovery::Update(const ImuData &imu_data, const MotorSpeedData &motor_speed_data)
{
    std::lock_guard<std::mutex> lock(data_mutex_);

    // 更新运动数据
    UpdateMotionData(imu_data, motor_speed_data);

    StuckRecoveryResult result;
    result.current_mode = current_recovery_mode_;
    result.recovery_attempts = recovery_attempts_;

    switch (state_)
    {
    case StuckRecoveryState::NORMAL:
    {
        // 检测是否被困
        if (DetectStuck())
        {
            LOG_WARN("[StuckRecovery] 检测到被困状态，开始脱困恢复");
            state_ = StuckRecoveryState::DETECTING;
            StartRecovery();
            result.is_stuck = true;
        }
        break;
    }

    case StuckRecoveryState::DETECTING:
    {
        // 再次确认被困状态
        if (DetectStuck())
        {
            state_ = StuckRecoveryState::RECOVERING;
            LOG_INFO("[StuckRecovery] 确认被困，开始执行恢复动作");
        }
        else
        {
            // 误判，恢复正常状态
            state_ = StuckRecoveryState::NORMAL;
            LOG_INFO("[StuckRecovery] 误判被困，恢复正常状态");
        }
        result.is_stuck = true;
        break;
    }

    case StuckRecoveryState::RECOVERING:
    {
        ExecuteRecoveryAction();
        CheckRecoveryProgress();
        result.is_stuck = true;

        // 计算恢复持续时间
        auto now = std::chrono::steady_clock::now();
        result.recovery_duration_ms = std::chrono::duration_cast<std::chrono::milliseconds>(
                                          now - recovery_start_time_)
                                          .count();

        break;
    }

    case StuckRecoveryState::COMPLETED:
    {
        result.recovery_success = true;
        state_ = StuckRecoveryState::NORMAL;
        LOG_INFO("[StuckRecovery] 脱困恢复完成，恢复正常状态");
        break;
    }
    }

    return result;
}

void StuckRecovery::Reset()
{
    std::lock_guard<std::mutex> lock(data_mutex_);

    state_ = StuckRecoveryState::NORMAL;
    current_recovery_mode_ = StuckRecoveryMode::NONE;
    motion_history_.clear();

    current_linear_velocity_ = 0.0f;
    current_angular_velocity_ = 0.0f;
    accumulated_displacement_x_ = 0.0f;
    accumulated_displacement_y_ = 0.0f;
    accumulated_rotation_ = 0.0f;

    recovery_attempts_ = 0;
    recovery_in_progress_ = false;

    // 重置IMU积分
    ResetImuIntegration();

    last_update_time_ = std::chrono::steady_clock::now();

    LOG_INFO("[StuckRecovery] 脱困恢复系统重置");
}

bool StuckRecovery::DetectStuck()
{
    // 分段检测：短、中、长三个时间窗口
    bool short_stuck = !HasMovementInWindow(param_.short_window_ms);
    bool medium_stuck = !HasMovementInWindow(param_.medium_window_ms);
    bool long_stuck = !HasMovementInWindow(param_.long_window_ms);

    // 检测轮子打滑情况
    bool wheel_slipping = DetectWheelSlipping();

    // 差异化检测逻辑：至少两个窗口检测到卡滞才确认被困
    int stuck_count = 0;
    if (short_stuck)
        stuck_count++;
    if (medium_stuck)
        stuck_count++;
    if (long_stuck)
        stuck_count++;

    // 被困判定条件：
    // 1. 至少两个时间窗口检测到无运动，或
    // 2. 长时间窗口检测到无运动且有轮子打滑，或
    // 3. 所有三个窗口都检测到无运动
    bool is_stuck = false;

    if (stuck_count >= 2)
    {
        is_stuck = true;
        LOG_WARN_THROTTLE(5000, "[StuckRecovery] 多窗口检测到被困: 短窗口={}, 中窗口={}, 长窗口={}, 打滑={}",
                          short_stuck, medium_stuck, long_stuck, wheel_slipping);
    }
    else if (long_stuck && wheel_slipping)
    {
        is_stuck = true;
        LOG_WARN_THROTTLE(5000, "[StuckRecovery] 长时间无运动且轮子打滑，判定为被困");
    }
    else if (short_stuck && medium_stuck && long_stuck)
    {
        is_stuck = true;
        LOG_WARN_THROTTLE(5000, "[StuckRecovery] 所有窗口检测到被困");
    }

    // 记录检测状态用于调试
    if (stuck_count > 0)
    {
        LOG_DEBUG_THROTTLE(2000, "[StuckRecovery] 检测状态: 短窗口={}, 中窗口={}, 长窗口={}, 打滑={}, 被困={}",
                           short_stuck, medium_stuck, long_stuck, wheel_slipping, is_stuck);
    }

    return is_stuck;
}

bool StuckRecovery::DetectWheelSlipping()
{
    // 检测轮子打滑：编码器显示有运动但IMU显示无实际运动
    if (current_imu_data_.system_timestamp == 0)
    {
        // 没有IMU数据，无法检测打滑
        return false;
    }

    // 检查是否有编码器运动
    bool encoder_has_motion = (std::abs(current_encoder_linear_velocity_) > param_.encoder_velocity_threshold) ||
                              (std::abs(current_encoder_angular_velocity_) > param_.encoder_velocity_threshold);

    // 检查IMU是否显示实际运动
    bool imu_has_motion = (std::abs(current_imu_data_.linear_acceleration_x) > param_.imu_acceleration_threshold) ||
                          (std::abs(current_imu_data_.linear_acceleration_y) > param_.imu_acceleration_threshold) ||
                          (std::abs(current_imu_data_.angular_velocity_z) > param_.imu_angular_velocity_threshold);

    // 打滑判定：编码器有运动但IMU无运动
    bool is_slipping = encoder_has_motion && !imu_has_motion;

    if (is_slipping)
    {
        LOG_DEBUG_THROTTLE(1000, "[StuckRecovery] 检测到轮子打滑: 编码器线速度={:.3f}, IMU加速度X={:.3f}, IMU角速度Z={:.3f}",
                           current_encoder_linear_velocity_,
                           current_imu_data_.linear_acceleration_x,
                           current_imu_data_.angular_velocity_z);
    }

    return is_slipping;
}

void StuckRecovery::StartRecovery()
{
    recovery_start_time_ = std::chrono::steady_clock::now();
    recovery_attempts_ = 0;
    recovery_in_progress_ = true;
    current_recovery_mode_ = StuckRecoveryMode::FORWARD;

    // 重置速度为初始值
    current_linear_velocity_ = param_.initial_linear_velocity;
    current_angular_velocity_ = param_.initial_angular_velocity;

    LOG_INFO("[StuckRecovery] 开始脱困恢复，初始模式: FORWARD");
}

void StuckRecovery::ExecuteRecoveryAction()
{
    auto now = std::chrono::steady_clock::now();

    // 检查是否需要执行新的动作
    if (std::chrono::duration_cast<std::chrono::milliseconds>(now - last_action_time_).count() >= param_.action_duration_ms + param_.action_interval_ms)
    {
        last_action_time_ = now;

        switch (current_recovery_mode_)
        {
        case StuckRecoveryMode::SINGLE_WHEEL_LEFT:
            ExecuteSingleWheelRotation(true);
            break;
        case StuckRecoveryMode::SINGLE_WHEEL_RIGHT:
            ExecuteSingleWheelRotation(false);
            break;
        case StuckRecoveryMode::FORWARD:
            ExecuteForwardBackward(true);
            break;
        case StuckRecoveryMode::BACKWARD:
            ExecuteForwardBackward(false);
            break;
        case StuckRecoveryMode::ROTATE_LEFT:
            ExecuteRotation(true);
            break;
        case StuckRecoveryMode::ROTATE_RIGHT:
            ExecuteRotation(false);
            break;
        case StuckRecoveryMode::ALTERNATING_PUSH:
            ExecuteAlternatingPush();
            break;
        default:
            break;
        }

        recovery_attempts_++;

        // 渐进式增加速度
        current_linear_velocity_ = std::min(current_linear_velocity_ + param_.velocity_increment,
                                            param_.max_linear_velocity);
        current_angular_velocity_ = std::min(current_angular_velocity_ + param_.velocity_increment,
                                             param_.max_angular_velocity);

        LOG_INFO("[StuckRecovery] 执行恢复动作: {}, 尝试次数: {}, 线速度: {:.2f}, 角速度: {:.2f}",
                 static_cast<int>(current_recovery_mode_), recovery_attempts_,
                 current_linear_velocity_, current_angular_velocity_);
    }
}

void StuckRecovery::CheckRecoveryProgress()
{
    // 检查是否有实际移动
    if (HasMovementInWindow(param_.short_window_ms))
    {
        LOG_INFO("[StuckRecovery] 检测到移动，脱困成功");
        state_ = StuckRecoveryState::COMPLETED;
        recovery_in_progress_ = false;
        return;
    }

    // 检查是否超过最大尝试次数
    if (recovery_attempts_ >= param_.max_recovery_attempts)
    {
        LOG_WARN("[StuckRecovery] 达到最大尝试次数，切换到下一个恢复模式");
        SwitchToNextMode();
        recovery_attempts_ = 0;

        // 重置速度
        current_linear_velocity_ = param_.initial_linear_velocity;
        current_angular_velocity_ = param_.initial_angular_velocity;
    }
}

bool StuckRecovery::HasMovementInWindow(uint32_t window_ms)
{
    if (motion_history_.empty())
        return false;

    auto now = std::chrono::steady_clock::now();
    uint64_t current_time = std::chrono::duration_cast<std::chrono::milliseconds>(
                                now.time_since_epoch())
                                .count();

    float total_displacement = 0.0f;
    float total_rotation = 0.0f;

    for (const auto &data : motion_history_)
    {
        if (current_time - data.timestamp <= window_ms)
        {
            total_displacement += std::sqrt(data.displacement_x * data.displacement_x +
                                            data.displacement_y * data.displacement_y);
            total_rotation += std::abs(data.rotation);
        }
    }

    bool has_movement = (total_displacement > param_.min_displacement_threshold) ||
                        (total_rotation > param_.min_rotation_threshold);

    LOG_DEBUG_THROTTLE(2000, "[StuckRecovery] 窗口{}ms内运动检查: 位移={:.3f}, 旋转={:.3f}, 有运动={}",
                       window_ms, total_displacement, total_rotation, has_movement);

    return has_movement;
}

void StuckRecovery::UpdateMotionData(const ImuData &imu_data, const MotorSpeedData &motor_speed_data)
{
    auto now = std::chrono::steady_clock::now();
    uint64_t current_time = std::chrono::duration_cast<std::chrono::milliseconds>(
                                now.time_since_epoch())
                                .count();

    // 计算时间差
    float dt = 0.0f;
    if (!motion_history_.empty())
    {
        dt = (current_time - motion_history_.back().timestamp) / 1000.0f; // 转换为秒
    }
    else
    {
        dt = 0.1f; // 默认100ms
    }

    // 从编码器计算线速度和角速度（用于打滑检测）
    float left_speed = motor_speed_data.motor_speed_left / 99.5f;   // RPM
    float right_speed = motor_speed_data.motor_speed_right / 99.5f; // RPM

    // 转换为线速度 (m/s)
    float left_velocity = (left_speed * 2 * M_PI * param_.wheel_radius) / 60.0f;
    float right_velocity = (right_speed * 2 * M_PI * param_.wheel_radius) / 60.0f;

    float encoder_linear_velocity = (left_velocity + right_velocity) / 2.0f;
    float encoder_angular_velocity = (right_velocity - left_velocity) / param_.wheel_base;

    // 保存编码器数据和IMU数据用于打滑检测
    current_encoder_linear_velocity_ = encoder_linear_velocity;
    current_encoder_angular_velocity_ = encoder_angular_velocity;
    current_imu_data_ = imu_data;
    current_motor_data_ = motor_speed_data;

    // 优先使用IMU数据计算实际运动（更可靠，不受打滑影响）
    float actual_linear_velocity = 0.0f;
    float actual_angular_velocity = 0.0f;

    if (imu_data.system_timestamp > 0)
    {
        // 使用IMU角速度
        actual_angular_velocity = imu_data.angular_velocity_z;

        // 通过IMU加速度积分估算线速度
        if (!imu_integration_initialized_)
        {
            InitializeImuIntegration(imu_data);
        }

        actual_linear_velocity = IntegrateImuVelocity(imu_data);

        // 如果IMU积分速度过小，可能是静止状态或积分漂移，使用编码器作为参考
        if (std::abs(actual_linear_velocity) < param_.imu_integration_reset_threshold &&
            std::abs(encoder_linear_velocity) > param_.encoder_velocity_threshold)
        {
            // 可能是轮子打滑或IMU积分漂移，重置积分
            ResetImuIntegration();
            actual_linear_velocity = 0.0f; // 认为实际无运动
        }
    }
    else
    {
        // 没有IMU数据时使用编码器数据
        actual_linear_velocity = encoder_linear_velocity;
        actual_angular_velocity = encoder_angular_velocity;
    }

    // 计算位移
    MotionData motion_data;
    motion_data.timestamp = current_time;
    motion_data.linear_velocity = actual_linear_velocity;
    motion_data.angular_velocity = actual_angular_velocity;

    if (dt > 0 && dt < 1.0f) // 合理的时间间隔
    {
        CalculateDisplacement(actual_linear_velocity, actual_angular_velocity, dt);
        motion_data.displacement_x = accumulated_displacement_x_;
        motion_data.displacement_y = accumulated_displacement_y_;
        motion_data.rotation = accumulated_rotation_;
    }
    else
    {
        motion_data.displacement_x = 0.0f;
        motion_data.displacement_y = 0.0f;
        motion_data.rotation = 0.0f;
    }

    motion_history_.push_back(motion_data);

    // 清理过期数据
    CleanOldData(param_.long_window_ms);

    last_update_time_ = now;
}

void StuckRecovery::CalculateDisplacement(float linear_vel, float angular_vel, float dt)
{
    // 简单的运动学积分
    float dx = linear_vel * cos(accumulated_rotation_) * dt;
    float dy = linear_vel * sin(accumulated_rotation_) * dt;
    float dtheta = angular_vel * dt;

    accumulated_displacement_x_ += dx;
    accumulated_displacement_y_ += dy;
    accumulated_rotation_ += dtheta;

    // 角度归一化到 [-π, π]
    while (accumulated_rotation_ > M_PI)
        accumulated_rotation_ -= 2 * M_PI;
    while (accumulated_rotation_ < -M_PI)
        accumulated_rotation_ += 2 * M_PI;
}

void StuckRecovery::CleanOldData(uint32_t max_age_ms)
{
    if (motion_history_.empty())
        return;

    uint64_t current_time = std::chrono::duration_cast<std::chrono::milliseconds>(
                                std::chrono::steady_clock::now().time_since_epoch())
                                .count();

    while (!motion_history_.empty() &&
           (current_time - motion_history_.front().timestamp) > max_age_ms)
    {
        motion_history_.pop_front();
    }
}

void StuckRecovery::ExecuteSingleWheelRotation(bool left_wheel)
{
    if (left_wheel)
    {
        // 左轮高速旋转，右轮慢速或静止
        float left_vel = current_angular_velocity_ * param_.wheel_base / 2.0f;
        float right_vel = current_angular_velocity_ * param_.wheel_base / 4.0f;
        float linear = (left_vel + right_vel) / 2.0f;
        float angular = (right_vel - left_vel) / param_.wheel_base;

        PublishVelocity(linear, angular, param_.action_duration_ms);
        LOG_INFO("[StuckRecovery] 执行左轮单独旋转");
    }
    else
    {
        // 右轮高速旋转，左轮慢速或静止
        float left_vel = current_angular_velocity_ * param_.wheel_base / 4.0f;
        float right_vel = current_angular_velocity_ * param_.wheel_base / 2.0f;
        float linear = (left_vel + right_vel) / 2.0f;
        float angular = (right_vel - left_vel) / param_.wheel_base;

        PublishVelocity(linear, angular, param_.action_duration_ms);
        LOG_INFO("[StuckRecovery] 执行右轮单独旋转");
    }
}

void StuckRecovery::ExecuteForwardBackward(bool forward)
{
    float linear = forward ? current_linear_velocity_ : -current_linear_velocity_;
    PublishVelocity(linear, 0.0f, param_.action_duration_ms);

    LOG_INFO("[StuckRecovery] 执行{}运动，速度: {:.2f}",
             forward ? "前进" : "后退", linear);
}

void StuckRecovery::ExecuteRotation(bool left_rotation)
{
    float angular = left_rotation ? current_angular_velocity_ : -current_angular_velocity_;
    PublishVelocity(0.0f, angular, param_.action_duration_ms);

    LOG_INFO("[StuckRecovery] 执行{}旋转，角速度: {:.2f}",
             left_rotation ? "左" : "右", angular);
}

void StuckRecovery::ExecuteAlternatingPush()
{
    static bool forward_phase = true;

    float linear = forward_phase ? current_linear_velocity_ : -current_linear_velocity_;
    PublishVelocity(linear, 0.0f, param_.action_duration_ms / 2);

    forward_phase = !forward_phase;

    LOG_INFO("[StuckRecovery] 执行交替推进，当前阶段: {}",
             forward_phase ? "前进" : "后退");
}

void StuckRecovery::PublishVelocity(float linear, float angular, uint64_t duration_ms)
{
    if (velocity_publish_callback_)
    {
        velocity_publish_callback_(linear, angular, duration_ms);
    }
    else
    {
        LOG_WARN("[StuckRecovery] 速度发布回调未设置");
    }
}

StuckRecoveryMode StuckRecovery::GetNextRecoveryMode()
{
    switch (current_recovery_mode_)
    {
    case StuckRecoveryMode::FORWARD:
        return StuckRecoveryMode::BACKWARD;
    case StuckRecoveryMode::BACKWARD:
        return StuckRecoveryMode::ROTATE_LEFT;
    case StuckRecoveryMode::ROTATE_LEFT:
        return StuckRecoveryMode::ROTATE_RIGHT;
    case StuckRecoveryMode::ROTATE_RIGHT:
        return StuckRecoveryMode::SINGLE_WHEEL_LEFT;
    case StuckRecoveryMode::SINGLE_WHEEL_LEFT:
        return StuckRecoveryMode::SINGLE_WHEEL_RIGHT;
    case StuckRecoveryMode::SINGLE_WHEEL_RIGHT:
        return StuckRecoveryMode::ALTERNATING_PUSH;
    case StuckRecoveryMode::ALTERNATING_PUSH:
        return StuckRecoveryMode::FORWARD; // 循环回到开始
    default:
        return StuckRecoveryMode::FORWARD;
    }
}

void StuckRecovery::SwitchToNextMode()
{
    StuckRecoveryMode next_mode = GetNextRecoveryMode();

    LOG_INFO("[StuckRecovery] 切换恢复模式: {} -> {}",
             static_cast<int>(current_recovery_mode_), static_cast<int>(next_mode));

    current_recovery_mode_ = next_mode;
}

void StuckRecovery::InitializeImuIntegration(const ImuData &imu_data)
{
    integrated_velocity_x_ = 0.0f;
    integrated_velocity_y_ = 0.0f;
    last_acceleration_x_ = imu_data.linear_acceleration_x;
    last_acceleration_y_ = imu_data.linear_acceleration_y;
    last_imu_timestamp_ = imu_data.system_timestamp;
    imu_integration_initialized_ = true;

    LOG_DEBUG("[StuckRecovery] IMU积分初始化完成");
}

float StuckRecovery::IntegrateImuVelocity(const ImuData &imu_data)
{
    if (!imu_integration_initialized_ || last_imu_timestamp_ == 0)
    {
        InitializeImuIntegration(imu_data);
        return 0.0f;
    }

    // 计算时间差（秒）
    float dt = (imu_data.system_timestamp - last_imu_timestamp_) / 1000.0f;

    // 防止异常时间差
    if (dt <= 0 || dt > 1.0f)
    {
        LOG_WARN("[StuckRecovery] 异常时间差: {:.3f}s，重置IMU积分", dt);
        ResetImuIntegration();
        return 0.0f;
    }

    // 去除重力加速度影响（简化处理，假设割草机在水平面上）
    float acc_x = imu_data.linear_acceleration_x;
    float acc_y = imu_data.linear_acceleration_y;

    // 低通滤波去除高频噪声
    acc_x = param_.imu_filter_alpha * last_acceleration_x_ + (1 - param_.imu_filter_alpha) * acc_x;
    acc_y = param_.imu_filter_alpha * last_acceleration_y_ + (1 - param_.imu_filter_alpha) * acc_y;

    // 死区处理，去除小幅度噪声
    if (std::abs(acc_x) < param_.imu_acceleration_deadzone)
        acc_x = 0.0f;
    if (std::abs(acc_y) < param_.imu_acceleration_deadzone)
        acc_y = 0.0f;

    // 梯形积分法计算速度
    integrated_velocity_x_ += (last_acceleration_x_ + acc_x) * dt / 2.0f;
    integrated_velocity_y_ += (last_acceleration_y_ + acc_y) * dt / 2.0f;

    // 速度衰减，防止积分漂移
    const float decay_factor = 0.99f; // 每次更新衰减1%
    integrated_velocity_x_ *= decay_factor;
    integrated_velocity_y_ *= decay_factor;

    // 计算合成线速度
    float linear_velocity = std::sqrt(integrated_velocity_x_ * integrated_velocity_x_ +
                                      integrated_velocity_y_ * integrated_velocity_y_);

    // 更新历史数据
    last_acceleration_x_ = acc_x;
    last_acceleration_y_ = acc_y;
    last_imu_timestamp_ = imu_data.system_timestamp;

    LOG_DEBUG_THROTTLE(1000, "[StuckRecovery] IMU积分: acc_x={:.3f}, acc_y={:.3f}, vel_x={:.3f}, vel_y={:.3f}, linear_vel={:.3f}",
                       acc_x, acc_y, integrated_velocity_x_, integrated_velocity_y_, linear_velocity);

    return linear_velocity;
}

void StuckRecovery::ResetImuIntegration()
{
    integrated_velocity_x_ = 0.0f;
    integrated_velocity_y_ = 0.0f;
    last_acceleration_x_ = 0.0f;
    last_acceleration_y_ = 0.0f;
    last_imu_timestamp_ = 0;
    imu_integration_initialized_ = false;

    LOG_DEBUG("[StuckRecovery] IMU积分已重置");
}

} // namespace fescue_iox
